<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Brand Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .test-buttons button {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Brand Login Test</h1>
        
        <div class="test-buttons">
            <button onclick="testBackendHealth()">Test Backend Health</button>
            <button onclick="testEmptyCampaigns()">Test Empty Campaigns</button>
            <button onclick="testAdminLogin()">Test Admin Login</button>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="SecurePass2024!@#$" required>
            </div>
            
            <button type="submit">Test Login</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        const BACKEND_URL = 'http://localhost:2340';
        
        async function testBackendHealth() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing backend health...';
            
            try {
                const response = await fetch(`${BACKEND_URL}/health`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ Backend Health Check Successful!\n\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Backend Health Check Failed!\n\nError: ${error.message}\n\nMake sure the backend server is running on port 2340`;
            }
        }
        
        async function testEmptyCampaigns() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing empty campaigns endpoint...';

            try {
                const response = await fetch(`${BACKEND_URL}/api/brand/campaigns?empty=true`);
                const data = await response.json();

                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ Empty Campaigns Test Successful!\n\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Empty Campaigns Test Failed!\n\nError: ${error.message}`;
            }
        }

        async function testAdminLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing admin login...';

            try {
                const response = await fetch(`${BACKEND_URL}/api/admin/auth`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: '<EMAIL>',
                        password: 'Wjdalsdnd0145!!'
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Admin Login Successful!\n\nUsername: <EMAIL>\nToken: ${data.token}\n\nYou can now access the admin dashboard!`;

                    // Test admin verification
                    try {
                        const verifyResponse = await fetch(`${BACKEND_URL}/api/admin/verify`, {
                            headers: {
                                'Authorization': data.token,
                                'Content-Type': 'application/json'
                            }
                        });

                        const verifyData = await verifyResponse.json();
                        resultDiv.innerHTML += `\n\n🔐 Admin Verification: ${verifyData.status === 'success' ? '✅ Success' : '❌ Failed'}`;

                    } catch (verifyError) {
                        resultDiv.innerHTML += `\n\n❌ Admin verification error: ${verifyError.message}`;
                    }

                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Admin Login Failed!\n\nMessage: ${data.message}`;
                }

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Admin Login Connection Error!\n\nError: ${error.message}`;
            }
        }
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = 'Testing login...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/auth/signin`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Login Successful!\n\nEmail: ${email}\nToken: ${data.token}\n\nYou can now test the brand dashboard!`;
                    
                    // Test accessing brand campaigns
                    try {
                        const campaignsResponse = await fetch(`${BACKEND_URL}/api/brand/campaigns`, {
                            headers: {
                                'Authorization': `Bearer ${data.token}`,
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        const campaignsData = await campaignsResponse.json();
                        resultDiv.innerHTML += `\n\n📊 Campaigns Data:\n${JSON.stringify(campaignsData, null, 2)}`;
                        
                        // Test empty campaigns
                        const emptyCampaignsResponse = await fetch(`${BACKEND_URL}/api/brand/campaigns?empty=true`, {
                            headers: {
                                'Authorization': `Bearer ${data.token}`,
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        const emptyCampaignsData = await emptyCampaignsResponse.json();
                        resultDiv.innerHTML += `\n\n📊 Empty Campaigns Data:\n${JSON.stringify(emptyCampaignsData, null, 2)}`;
                        
                    } catch (campaignError) {
                        resultDiv.innerHTML += `\n\n❌ Campaign fetch error: ${campaignError.message}`;
                    }
                    
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Login Failed!\n\nMessage: ${data.message}`;
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Connection Error!\n\nError: ${error.message}\n\nMake sure the backend server is running on port 2340`;
            }
        });
    </script>
</body>
</html>
