#!/usr/bin/env node

// Test script to verify API prefix approach works
const axios = require('axios');

const BACKEND_URL = 'https://guideway-consulting-production.up.railway.app/api';

async function testApiPrefix() {
  console.log('🧪 Testing API Prefix Approach');
  console.log('==============================');
  console.log(`🌐 Backend URL with /api prefix: ${BACKEND_URL}`);
  console.log('');

  let allPassed = true;

  // Test 1: Health Check (should work without /api since it's at root)
  console.log('🏥 Test 1: Health Check (root endpoint)');
  try {
    const healthUrl = 'https://guideway-consulting-production.up.railway.app/health';
    const response = await axios.get(healthUrl, { timeout: 10000 });
    if (response.status === 200 && response.data.status === 'healthy') {
      console.log('   ✅ Health check passed');
      console.log(`   📊 Status: ${response.data.status}`);
    } else {
      console.log('   ❌ Health check failed');
      allPassed = false;
    }
  } catch (error) {
    console.log('   ❌ Health check failed');
    console.log(`   🚨 Error: ${error.message}`);
    allPassed = false;
  }

  console.log('');

  // Test 2: Admin Auth (should work with /api prefix)
  console.log('🔐 Test 2: Admin Auth Endpoint');
  try {
    const response = await axios.post(`${BACKEND_URL}/admin/auth`, {
      username: '<EMAIL>',
      password: 'Wjdalsdnd0145!!'
    }, { timeout: 10000 });
    
    if (response.status === 200 && response.data.status === 'success') {
      console.log('   ✅ Admin auth passed');
      console.log('   🔑 Token generated successfully');
      
      // Test 3: Admin Verify (should work with /api prefix)
      console.log('');
      console.log('🔍 Test 3: Admin Verify Endpoint');
      try {
        const verifyResponse = await axios.get(`${BACKEND_URL}/admin/verify`, {
          headers: { authorization: response.data.token },
          timeout: 10000
        });
        
        if (verifyResponse.status === 200 && verifyResponse.data.status === 'success') {
          console.log('   ✅ Admin verify passed');
        } else {
          console.log('   ❌ Admin verify failed');
          allPassed = false;
        }
      } catch (error) {
        console.log('   ❌ Admin verify failed');
        console.log(`   🚨 Error: ${error.message}`);
        allPassed = false;
      }
      
    } else {
      console.log('   ❌ Admin auth failed');
      console.log('   📄 Response:', response.data);
      allPassed = false;
    }
  } catch (error) {
    console.log('   ❌ Admin auth failed');
    console.log(`   🚨 Error: ${error.message}`);
    allPassed = false;
  }

  console.log('');

  // Test 4: Public Campaigns (should work with /api prefix)
  console.log('📋 Test 4: Public Campaigns Endpoint');
  try {
    const response = await axios.get(`${BACKEND_URL}/user/campaigns`, { timeout: 10000 });
    if (response.status === 200) {
      console.log('   ✅ Public campaigns passed');
      console.log(`   📊 Campaigns found: ${response.data.campaigns?.length || 0}`);
    } else {
      console.log('   ❌ Public campaigns failed');
      allPassed = false;
    }
  } catch (error) {
    console.log('   ❌ Public campaigns failed');
    console.log(`   🚨 Error: ${error.message}`);
    allPassed = false;
  }

  console.log('');

  // Summary
  console.log('📋 Test Summary');
  console.log('===============');
  if (allPassed) {
    console.log('✅ All tests passed! API prefix approach is working correctly.');
    console.log('🚀 Frontend can use VITE_BACKEND_URL with /api suffix.');
    console.log('');
    console.log('✅ Recommended frontend .env configuration:');
    console.log('   VITE_BACKEND_URL=https://guideway-consulting-production.up.railway.app/api');
  } else {
    console.log('❌ Some tests failed. Check the issues above.');
    console.log('🔧 May need to fix backend routes or deployment.');
  }

  console.log('');
  console.log('🔗 Next Steps:');
  console.log('1. Update frontend .env with /api suffix');
  console.log('2. Remove /api prefix from individual API calls');
  console.log('3. Test frontend admin login');
  console.log('4. Deploy frontend to Vercel');

  process.exit(allPassed ? 0 : 1);
}

// Run tests
testApiPrefix().catch(error => {
  console.error('🚨 Test runner failed:', error.message);
  process.exit(1);
});
