# 🚀 Deployment Configuration

## URLs
- **Frontend (Vercel)**: https://guideway-consulting.vercel.app
- **Backend (Railway)**: https://guideway-consulting-production.up.railway.app

## 🚂 Railway Environment Variables (Backend)

Set these in Railway Dashboard → Your Project → Variables:

```env
PORT=8080
NODE_ENV=production
MONGO_URL=mongodb+srv://abhisheksingh4115:<EMAIL>/Practice-db?retryWrites=true&w=majority
SECRET_KEY=iREJP1r+kdPWJ7VWcz/BtXibAWhFTEIKnWdGiZ9ZAI4=
ADMIN_USER=<EMAIL>
ADMIN_HASHED_PASS=$2b$10$kthSu.4WHdClXAhEdcEbTeChxrfKC0hqidI3sDJv3uDTTa56KjbR2
CLOUDINARY_CLOUD_NAME=drujwoine
CLOUDINARY_API_KEY=683974564338127
CLOUDINARY_API_SECRET=J8UG31wXEm-reuECSmdmQbSy3c4
FRONTEND_URL=https://guideway-consulting.vercel.app
RECAPTCHA_SECRET_KEY=6Ld-zC4rAAAAAAVVbpXmfeq9st9SZh0nT-BAceSS
SMTP_HOST=smtp-relay.brevo.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_SEND_EMAIL=<EMAIL>
SMTP_PASS=wHnj9fkskspqrmev6rce0tzd
SMTP_API_KEY=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
```

## 🌐 Vercel Environment Variables (Frontend)

Set these in Vercel Dashboard → Your Project → Settings → Environment Variables:

```env
VITE_BACKEND_URL=https://guideway-consulting-production.up.railway.app/api
VITE_RECAPTCHA_SITE_KEY=6Ld-zC4rAAAAAPjAuASmaRY-J4hie_YZPF63OuHo
VITE_GOOGLE_CLIENT_ID=************-43j6p7k79nfi39d5u7knmai9nkla1tgb.apps.googleusercontent.com
```

## 🧪 Testing URLs

### Backend Health Check:
```bash
curl https://guideway-consulting-production.up.railway.app/health
```

### Backend API Root:
```bash
curl https://guideway-consulting-production.up.railway.app/
```

### Frontend:
```
https://guideway-consulting.vercel.app
```

## 📋 Deployment Checklist

### Backend (Railway):
- [ ] Environment variables set
- [ ] Health check returns 200
- [ ] Database connection working
- [ ] CORS configured for Vercel URL

### Frontend (Vercel):
- [ ] Environment variables set
- [ ] Build successful
- [ ] Routing working (404 fix applied)
- [ ] API calls connecting to Railway

## 🔧 Troubleshooting

### If frontend can't connect to backend:
1. Check CORS settings in backend
2. Verify VITE_BACKEND_URL in Vercel
3. Check Railway logs for errors
4. Test backend health endpoint

### If 404 errors on frontend routes:
- Ensure vercel.json is deployed
- Check _redirects file in public folder

### If database connection fails:
- Verify MONGO_URL in Railway
- Check MongoDB Atlas whitelist (should allow all IPs: 0.0.0.0/0)
