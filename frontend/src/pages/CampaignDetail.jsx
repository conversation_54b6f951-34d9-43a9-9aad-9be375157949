import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import useAuthStore from "../state/atoms";
import { useNavigate } from "react-router-dom";
import config from "../config";
import axios from "axios";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import SubmitApplication from "../components/application/submitApplication";
import SuccessPopup from "../components/successPopup";
import { Helmet } from "react-helmet";
import Cookies from "js-cookie";
import { Accordion, AccordionGroup } from "../components/ui/Accordion";
import CopyButton from "../components/ui/CopyButton";
import {
  GiftIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  HashtagIcon,
  PhotoIcon,
  InformationCircleIcon,
  TagIcon
} from "@heroicons/react/24/outline";

const CampaignDetail = () => {
  const [loading, setloading] = useState(true);
  const [campaign, setCampaign] = useState({});
  const Navigate = useNavigate();
  const { isLogin, User } = useAuthStore();
  const { campaignId } = useParams();
  const [isOpen, setIsOpen] = useState(false);
  const [success, setSuccess] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [submittedUrls, setSubmittedUrls] = useState(null);
  const [instagramUrls, setInstagramUrls] = useState("");
  const [youtubeUrls, setYoutubeUrls] = useState("");
  const [tiktokUrls, setTiktokUrls] = useState("");
  const [allowReuse, setAllowReuse] = useState(false);
  const [appliedStatus, setAppliedStatus] = useState(""); // "Approved", "Rejected", "Pending", ""
  const [applicantsCount, setApplicantsCount] = useState(0);
  const [appliedThisMonth, setAppliedThisMonth] = useState(0);
  const [campaignStatus, setCampaignStatus] = useState("Recruiting"); // or "Closed"
  const [bid, setBid] = useState("");
  const [bidError, setBidError] = useState("");

  const minFollowers = campaign?.min_required_followers;
  const creatorFollowers = User?.verified_follower_count;

  const isFollowerEligible =
    minFollowers == null ||
    (creatorFollowers != null && creatorFollowers >= minFollowers);

  const followerError =
    minFollowers != null && (creatorFollowers == null || creatorFollowers < minFollowers)
      ? `You need at least ${minFollowers.toLocaleString()} verified followers to apply.`
      : null;

  useEffect(() => {
    if (appliedStatus === "Approved") {
      fetchSubmittedData();
    }
  }, [appliedStatus]);

  async function fetchSubmittedData() {
    try {
      const token = Cookies.get("token") || localStorage.getItem("token");
      const res = await axios.get(
        `${config.BACKEND_URL}/user/campaign-submission/${campaignId}`,
        {
          headers: {
            Authorization: token, // <-- THIS IS IMPORTANT
          },
        }
      );

      if (res.data.status === "success") {
        setSubmittedUrls({
          instagram: res.data.data.instagram_urls,
          youtube: res.data.data.youtube_urls,
          tiktok: res.data.data.tiktok_urls,
        });
      }
    } catch (err) {
      console.error(
        "Error fetching submitted content:",
        err.response?.data || err.message
      );
    }
  }

  const handleSubmitContent = async () => {
    const token = Cookies.get("token") || localStorage.getItem("token");
    try {
      if (campaign?.campaignType === "paid" && campaign?.pricingModel === "bidding") {
        const min = campaign.minBid || 1;
        const max = campaign.maxBid || 10000;
        const bidValue = Number(bid);
        if (isNaN(bidValue) || bidValue < min || bidValue > max) {
          setBidError(`Bid must be between $${min} and $${max}.`);
          setLoading(false);
          return;
        }
        setBidError("");
      }

      const res = await axios.post(
        `${config.BACKEND_URL}/user/campaign-submission`,
        {
          campaign_id: campaignId,
          email: User.email,
          instagram_urls: instagramUrls.split(",").map((url) => url.trim()),
          youtube_urls: youtubeUrls.split(",").map((url) => url.trim()),
          tiktok_urls: tiktokUrls.split(",").map((url) => url.trim()),
          allow_brand_reuse: allowReuse,
        },
        {
          headers: {
            Authorization: token,
          },
        }
      );

      alert("Content submitted successfully!");

      // Show URLs after submit
      setSubmittedUrls({
        instagram: instagramUrls.split(",").map((url) => url.trim()),
        youtube: youtubeUrls.split(",").map((url) => url.trim()),
        tiktok: tiktokUrls.split(",").map((url) => url.trim()),
      });

      setShowSubmitModal(false); // close modal
    } catch (error) {
      console.error("Submission failed:", error);
      alert("Something went wrong while submitting.");
    }
  };

  useEffect(() => {
    if (User?.email && campaignId) {
      getUserApplicationStatus();
    }
  }, [User, campaignId]);

  async function getUserApplicationStatus() {
    try {
      const token = Cookies.get("token") || localStorage.getItem("token");
      const res = await axios.get(
        `${config.BACKEND_URL}/user/campaigns/appliedCampaigns`,
        {
          headers: {
            authorization: token,
          },
        }
      );

      if (res.data.status === "success") {
        setAppliedThisMonth(res.data.appliedThisMonth || 0);
        const found = res.data.campaigns.find(
          (c) => String(c.id) === String(campaignId)
        );
        if (found) {
          setAppliedStatus(found.applicationStatus);
        }
      }
    } catch (err) {
      console.error("Error fetching applied status:", err);
    }
  }

  useEffect(() => {
    getCampaign();
  }, [campaignId]);

  async function getCampaign() {
    try {
      const res = await axios.get(
        `${config.BACKEND_URL}/user/campaigns/${campaignId}/${User.email}`
      );
      if (res.data.status === "success") {
        console.log(res.data.campaign);
        const c = res.data.campaign;
        setCampaign(c);
        setApplicantsCount(res.data.applicantsCount || 0);
        setCampaignStatus(res.data.campaignStatus || "Recruiting");
      }
    } catch {
      // error handling
    } finally {
      setloading(false);
    }
  }

  const handleOutsideClick = (e) => {
    if (e.target.id === "sidebar-overlay") {
      setIsOpen(false);
    }
  };

  let metaTags = [];

  if (campaign && !loading) {
    metaTags = [
      <meta name="robots" content="index, follow" key="robots" />,
      <meta
        property="og:title"
        content={campaign.campaignTitle || "N/A"}
        key="og-title"
      />,
      <meta
        property="og:description"
        content={campaign.productDescription || "N/A"}
        key="og-description"
      />,
    ];
  } else {
    metaTags = [
      <meta name="robots" content="noindex, nofollow" key="robots" />,
    ];
  }

  function toCamelCase(str) {
    if (!str) return "";
    return str
      .replace(/[^a-zA-Z0-9 ]/g, "") // Remove special characters
      .trim()
      .split(/\s+/) // Split by whitespace
      .map((word, index) =>
        index === 0
          ? word.toLowerCase()
          : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      )
      .join("");
  }

  return (
    <div className="min-h-[85vh] bg-black px-4 py-8">
      <Helmet>
        <title>{campaign.campaignTitle || "Campaign Details"}</title>
        {metaTags}
      </Helmet>
      {loading ? (
        <CampaignSkeleton />
      ) : (
        <div className="max-w-4xl mx-auto">
          <AccordionGroup>
            {/* 1️⃣ Campaign Overview - Always Open */}
            <Accordion
              title="Campaign Overview"
              alwaysOpen={true}
              icon="📋"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Left Column - Campaign Info */}
                <div className="space-y-4">
                  <div>
                    <p className="text-gray-400 text-sm FontNoto">Brand</p>
                    <p className="text-white text-lg font-semibold FontNoto">{campaign.brandName || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm FontNoto">Campaign</p>
                    <p className="text-white text-lg font-semibold FontNoto">{campaign.campaignTitle || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm FontNoto">Product</p>
                    <p className="text-white text-lg font-semibold FontNoto">{campaign.productName || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm FontNoto">Category</p>
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30">
                      {campaign.category || "N/A"}
                    </span>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm FontNoto">Platform</p>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {campaign.contentFormat?.map((platform, index) => (
                        <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-500/20 text-purple-300 border border-purple-500/30">
                          {platform}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Right Column - Key Details */}
                <div className="space-y-4">
                  <div>
                    <p className="text-gray-400 text-sm FontNoto">Deadline</p>
                    <div className="flex items-center gap-2 mt-1">
                      <CalendarIcon className="w-4 h-4 text-gray-400" />
                      <p className="text-white FontNoto">{campaign.deadline ? campaign.deadline.split("T")[0] : "N/A"}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      {campaign.campaignType === "paid" ? (
                        <>
                          <CurrencyDollarIcon className="w-5 h-5 text-green-400" />
                          <span className="text-green-400 font-semibold FontNoto">
                            Paid: ${campaign.fixedPrice || `${campaign.minBid}-${campaign.maxBid}`}
                          </span>
                        </>
                      ) : (
                        <>
                          <GiftIcon className="w-5 h-5 text-yellow-400" />
                          <span className="text-yellow-400 font-semibold FontNoto">Free Product</span>
                        </>
                      )}
                    </div>
                  </div>

                  <div>
                    <p className="text-gray-400 text-sm FontNoto">Required Content</p>
                    <p className="text-white FontNoto">
                      {campaign.requestedContent?.videos > 0 && `${campaign.requestedContent.videos} Video${campaign.requestedContent.videos > 1 ? 's' : ''}`}
                      {campaign.requestedContent?.videos > 0 && campaign.requestedContent?.photos > 0 && " + "}
                      {campaign.requestedContent?.photos > 0 && `${campaign.requestedContent.photos} Photo${campaign.requestedContent.photos > 1 ? 's' : ''}`}
                      {!campaign.requestedContent?.videos && !campaign.requestedContent?.photos && "1 Video Required"}
                    </p>
                  </div>

                  <div>
                    <p className="text-gray-400 text-sm FontNoto">Status</p>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      campaignStatus === "Closed"
                        ? "bg-red-500/20 text-red-300 border border-red-500/30"
                        : "bg-green-500/20 text-green-300 border border-green-500/30"
                    }`}>
                      {campaignStatus}
                    </span>
                  </div>
                </div>
              </div>
            </Accordion>

            {/* 2️⃣ Content Guidelines - Always Open */}
            <Accordion
              title="Content Guidelines"
              alwaysOpen={true}
              icon="📝"
            >
              <div className="space-y-6">
                {/* Hashtags */}
                {campaign.requiredHashtags?.length > 0 && (
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <p className="text-gray-400 text-sm FontNoto">Required Hashtags</p>
                      <CopyButton
                        text={campaign.requiredHashtags.join(" ")}
                        label="Copy All"
                        size="sm"
                        variant="secondary"
                      />
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {campaign.requiredHashtags.map((hashtag, index) => (
                        <span key={index} className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30">
                          <HashtagIcon className="w-3 h-3" />
                          {hashtag.replace('#', '')}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Mention Handle */}
                {campaign.mentionHandle && (
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <p className="text-gray-400 text-sm FontNoto">@Mention</p>
                      <CopyButton
                        text={campaign.mentionHandle}
                        label="Copy"
                        size="sm"
                        variant="secondary"
                      />
                    </div>
                    <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium bg-purple-500/20 text-purple-300 border border-purple-500/30">
                      @{campaign.mentionHandle?.replace('@', '')}
                    </span>
                  </div>
                )}

                {/* Tone Guide */}
                {campaign.toneGuide && (
                  <div>
                    <p className="text-gray-400 text-sm FontNoto mb-2">Tone</p>
                    <p className="text-gray-200 FontNoto">{campaign.toneGuide}</p>
                  </div>
                )}

                {/* What You'll Get */}
                <div>
                  <p className="text-gray-400 text-sm FontNoto mb-2">What You'll Receive</p>
                  <p className="text-gray-200 FontNoto">{campaign.influencersReceive || "N/A"}</p>
                </div>

                {/* Requirements */}
                <div>
                  <p className="text-gray-400 text-sm FontNoto mb-2">Requirements</p>
                  <p className="text-gray-200 FontNoto">{campaign.participationRequirements || "N/A"}</p>
                </div>
              </div>
            </Accordion>

            {/* 3️⃣ Product Info & Images - Collapsed by Default */}
            <Accordion
              title="Product Info & Images"
              defaultOpen={false}
              icon="📷"
            >
              <div className="space-y-6">
                {/* Product Description */}
                <div>
                  <p className="text-gray-400 text-sm FontNoto mb-2">Product Description</p>
                  <p className="text-gray-200 FontNoto">{campaign.productDescription || "N/A"}</p>
                </div>

                {/* Product Images Gallery */}
                {campaign.productImages?.length > 0 && (
                  <div>
                    <p className="text-gray-400 text-sm FontNoto mb-3">Product Images</p>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {campaign.productImages.map((image, index) => (
                        <div key={index} className="aspect-square bg-white/5 rounded-lg overflow-hidden">
                          <img
                            src={image}
                            alt={`Product ${index + 1}`}
                            className="w-full h-full object-cover hover:scale-105 transition-transform duration-200 cursor-pointer"
                            onClick={() => window.open(image, '_blank')}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Category-specific details would go here */}
                {campaign.beautyDetails && (
                  <div>
                    <p className="text-gray-400 text-sm FontNoto mb-2">Beauty Details</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      {campaign.beautyDetails.skinType && (
                        <div>
                          <span className="text-gray-400">Skin Type:</span>
                          <span className="text-white ml-2">{campaign.beautyDetails.skinType}</span>
                        </div>
                      )}
                      {campaign.beautyDetails.keyIngredients && (
                        <div>
                          <span className="text-gray-400">Key Ingredients:</span>
                          <span className="text-white ml-2">{campaign.beautyDetails.keyIngredients}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </Accordion>

            {/* 4️⃣ Pricing & Required Content (Paid only) - Collapsed by Default */}
            {campaign.campaignType === "paid" && (
              <Accordion
                title="Reward & Deliverables"
                defaultOpen={false}
                icon="💰"
              >
                <div className="space-y-6">
                  {/* Pricing Model */}
                  <div>
                    <p className="text-gray-400 text-sm FontNoto mb-3">Pricing Model</p>
                    <div className="space-y-2">
                      {campaign.pricingModel === "fixed" ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 rounded-full bg-green-500"></div>
                          <span className="text-white FontNoto">Fixed Price: ${campaign.fixedPrice}</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 rounded-full bg-blue-500"></div>
                          <span className="text-white FontNoto">Bidding: ${campaign.minBid} - ${campaign.maxBid}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Requested Content */}
                  {(campaign.requestedContent?.videos > 0 || campaign.requestedContent?.photos > 0) && (
                    <div>
                      <p className="text-gray-400 text-sm FontNoto mb-3">Requested Content</p>
                      <div className="space-y-2">
                        {campaign.requestedContent.videos > 0 && (
                          <div className="flex items-center gap-2">
                            <PhotoIcon className="w-4 h-4 text-blue-400" />
                            <span className="text-white FontNoto">TikTok Videos: {campaign.requestedContent.videos}</span>
                          </div>
                        )}
                        {campaign.requestedContent.photos > 0 && (
                          <div className="flex items-center gap-2">
                            <PhotoIcon className="w-4 h-4 text-purple-400" />
                            <span className="text-white FontNoto">Static Instagram Posts: {campaign.requestedContent.photos}</span>
                          </div>
                        )}
                        {campaign.requestedContent.notes && (
                          <div className="mt-3">
                            <p className="text-gray-400 text-sm FontNoto mb-1">Additional Notes</p>
                            <p className="text-gray-200 FontNoto text-sm">{campaign.requestedContent.notes}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </Accordion>
            )}

            {/* 5️⃣ View Product Details (Category Info) - Collapsed by Default */}
            <Accordion
              title="View Product Details"
              defaultOpen={false}
              icon="💄"
            >
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-gray-400 text-sm FontNoto mb-1">Category</p>
                    <p className="text-white FontNoto">{campaign.category || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm FontNoto mb-1">Product Type</p>
                    <p className="text-white FontNoto">{campaign.productName || "N/A"}</p>
                  </div>
                </div>

                {/* Category-specific details */}
                {campaign.beautyDetails && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {campaign.beautyDetails.skinType && (
                      <div>
                        <p className="text-gray-400 text-sm FontNoto mb-1">Skin Type</p>
                        <p className="text-white FontNoto">{campaign.beautyDetails.skinType}</p>
                      </div>
                    )}
                    {campaign.beautyDetails.keyIngredients && (
                      <div>
                        <p className="text-gray-400 text-sm FontNoto mb-1">Key Ingredients</p>
                        <p className="text-white FontNoto">{campaign.beautyDetails.keyIngredients}</p>
                      </div>
                    )}
                    {campaign.beautyDetails.scent && (
                      <div>
                        <p className="text-gray-400 text-sm FontNoto mb-1">Scent</p>
                        <p className="text-white FontNoto">{campaign.beautyDetails.scent}</p>
                      </div>
                    )}
                    {campaign.beautyDetails.texture && (
                      <div>
                        <p className="text-gray-400 text-sm FontNoto mb-1">Texture</p>
                        <p className="text-white FontNoto">{campaign.beautyDetails.texture}</p>
                      </div>
                    )}
                    {campaign.beautyDetails.usageTiming && (
                      <div>
                        <p className="text-gray-400 text-sm FontNoto mb-1">Usage Timing</p>
                        <p className="text-white FontNoto">{campaign.beautyDetails.usageTiming}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </Accordion>
          </AccordionGroup>

          {/*  Apply Now CTA - Fixed at bottom */}
          <div className="sticky bottom-6 mt-8 z-10">
            <div className="bg-black/80 backdrop-blur-sm border border-white/10 rounded-2xl p-4">
              {appliedStatus === "Approved" && (
                <Link to={`/AddPostUrl/${campaignId}`} className="block mb-4">
                  <button className="w-full bg-green-600 text-white font-semibold px-6 py-3 rounded-full hover:bg-green-700 transition-colors">
                    Submit Content
                  </button>
                </Link>
              )}

              <button
                onClick={() => {
                  if (!isLogin) {
                    Navigate("/signin");
                    return;
                  }
                  setIsOpen(true);
                }}
                disabled={
                  !!appliedStatus ||
                  campaignStatus === "Closed" ||
                  appliedThisMonth >= 5
                }
                className={`w-full flex justify-center items-center font-semibold py-3 px-8 rounded-full transition-all duration-300 transform FontNoto
                  ${
                    appliedStatus === "Approved"
                      ? "bg-green-600 text-white cursor-default"
                      : appliedStatus === "Rejected"
                      ? "bg-red-600 text-white cursor-default"
                      : appliedStatus === "Pending"
                      ? "bg-yellow-400 text-black cursor-default"
                      : "text-black bg-[#facc15] hover:bg-[#ffb703] hover:scale-105 hover:shadow-lg"
                  } shadow-md`}
              >
                {appliedStatus === "Approved"
                  ? " Approved"
                  : appliedStatus === "Rejected"
                  ? "❌ Rejected"
                  : appliedStatus === "Pending"
                  ? "⏳ Pending"
                  : campaignStatus === "Closed"
                  ? "Campaign Closed"
                  : appliedThisMonth >= 5
                  ? "Limit Reached"
                  : " Apply to Campaign"}
              </button>
            </div>
          </div>
        </div>
      )}

      {showSubmitModal && (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-[10000]">
          <div className="bg-[#1e1e1e] p-6 rounded-lg max-w-[500px] w-full shadow-xl">
            <h3 className="text-white text-xl font-semibold mb-4">
              Submit Your Content
            </h3>

            {!isFollowerEligible && (
              <div className="text-red-500 font-semibold mb-4">{followerError}</div>
            )}

            <label className="text-white block mb-1">
              Instagram URLs (comma-separated)
            </label>
            <input
              type="text"
              className="w-full p-2 mb-3 rounded bg-black/40 text-white border border-gray-500"
              value={instagramUrls}
              onChange={(e) => setInstagramUrls(e.target.value)}
            />

            <label className="text-white block mb-1">
              YouTube URLs (comma-separated)
            </label>
            <input
              type="text"
              className="w-full p-2 mb-3 rounded bg-black/40 text-white border border-gray-500"
              value={youtubeUrls}
              onChange={(e) => setYoutubeUrls(e.target.value)}
            />

            <label className="text-white block mb-1">
              TikTok URLs (comma-separated)
            </label>
            <input
              type="text"
              className="w-full p-2 mb-3 rounded bg-black/40 text-white border border-gray-500"
              value={tiktokUrls}
              onChange={(e) => setTiktokUrls(e.target.value)}
            />

            <label className="text-white flex items-center gap-2 mb-4">
              <input
                type="checkbox"
                checked={allowReuse}
                onChange={(e) => setAllowReuse(e.target.checked)}
              />
              I authorize the brand to reuse my submitted content.
            </label>

            {campaign?.campaignType === "paid" && campaign?.pricingModel === "bidding" && (
              <div>
                <label htmlFor="bid" className="block text-sm font-medium text-gray-300 mb-1">
                  Enter your proposed bid (USD)
                </label>
                <input
                  id="bid"
                  name="bid"
                  type="number"
                  min={campaign.minBid || 1}
                  max={campaign.maxBid || 10000}
                  value={bid}
                  onChange={e => setBid(e.target.value)}
                  placeholder={`Enter a bid between $${campaign.minBid || 1} and $${campaign.maxBid || 10000}`}
                  className="w-full bg-[#575757] px-3 py-2 rounded-md"
                  required
                />
                {bidError && <p className="text-red-500 text-xs mt-1">{bidError}</p>}
              </div>
            )}

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowSubmitModal(false)}
                className="text-white px-4 py-2 rounded bg-gray-600 hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmitContent}
                className="bg-yellow-400 text-black px-4 py-2 rounded hover:bg-yellow-500 font-semibold"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
      {isOpen && (
        <div
          id="sidebar-overlay"
          className="fixed inset-0 bg-[#0000008a] bg-opacity-50 z-9999"
          onClick={handleOutsideClick}
        ></div>
      )}
      <SubmitApplication
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        campaignId={campaignId}
        setSuccess={setSuccess}
        campaign={campaign}
      ></SubmitApplication>
      <SuccessPopup
        show={success}
        onClose={() => {
          setAppliedStatus("Pending");
          setCampaign((prev) => ({ ...prev, applied: true }));
          setSuccess(false);
        }}
      ></SuccessPopup>
    </div>
  );
};

const CampaignSkeleton = () => {
  return (
    <div className="max-w-4xl mx-auto animate-pulse">
      <div className="space-y-4">
        {/* Campaign Overview Skeleton */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="h-4 bg-gray-300 rounded w-1/3"></div>
              <div className="h-6 bg-gray-300 rounded w-2/3"></div>
              <div className="h-4 bg-gray-300 rounded w-1/3"></div>
              <div className="h-6 bg-gray-300 rounded w-1/2"></div>
            </div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-300 rounded w-1/3"></div>
              <div className="h-6 bg-gray-300 rounded w-2/3"></div>
              <div className="h-4 bg-gray-300 rounded w-1/3"></div>
              <div className="h-6 bg-gray-300 rounded w-1/2"></div>
            </div>
          </div>
        </div>

        {/* Content Guidelines Skeleton */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="h-6 bg-gray-300 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            <div className="h-4 bg-gray-300 rounded w-1/4"></div>
            <div className="flex gap-2">
              <div className="h-8 bg-gray-300 rounded-full w-20"></div>
              <div className="h-8 bg-gray-300 rounded-full w-24"></div>
              <div className="h-8 bg-gray-300 rounded-full w-16"></div>
            </div>
            <div className="h-4 bg-gray-300 rounded w-full"></div>
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
          </div>
        </div>

        {/* Additional Accordion Skeletons */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-300 rounded w-full mb-2"></div>
          <div className="h-4 bg-gray-300 rounded w-2/3"></div>
        </div>

        {/* Apply Button Skeleton */}
        <div className="sticky bottom-6 mt-8">
          <div className="bg-black/80 backdrop-blur-sm border border-white/10 rounded-2xl p-4">
            <div className="h-12 bg-gray-300 rounded-full w-full"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CampaignDetail;
