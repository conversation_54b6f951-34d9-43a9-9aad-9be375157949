# 🚂 Railway Deployment Guide - Guideway Consulting

## 🎯 Deployment Configuration

**Frontend URL**: https://guideway-consulting.vercel.app  
**Backend URL**: Will be provided by Railway after deployment

## 🚀 Step-by-Step Deployment

### Step 1: Railway Project Setup

1. Go to [Railway.app](https://railway.app)
2. Create a new project
3. Connect your GitHub repository
4. Select the `backend` folder as the root directory

### Step 2: Set Environment Variables

In Railway Dashboard → Your Project → Variables, add these variables:

```env
NODE_ENV=production
FRONTEND_URL=https://guideway-consulting.vercel.app
MONGO_URL=mongodb+srv://abhisheksingh4115:<EMAIL>/Practice-db?retryWrites=true&w=majority
SECRET_KEY=iREJP1r+kdPWJ7VWcz/BtXibAWhFTEIKnWdGiZ9ZAI4=
ADMIN_USER=<EMAIL>
ADMIN_HASHED_PASS=$2b$10$kthSu.4WHdClXAhEdcEbTeChxrfKC0hqidI3sDJv3uDTTa56KjbR2
CLOUDINARY_CLOUD_NAME=drujwoine
CLOUDINARY_API_KEY=683974564338127
CLOUDINARY_API_SECRET=J8UG31wXEm-reuECSmdmQbSy3c4
RECAPTCHA_SECRET_KEY=6Ld-zC4rAAAAAAVVbpXmfeq9st9SZh0nT-BAceSS
GOOGLE_CLIENT_ID=************-43j6p7k79nfi39d5u7knmai9nkla1tgb.apps.googleusercontent.com
BREVO_SMTP_HOST=smtp-relay.brevo.com
BREVO_SMTP_PORT=587
BREVO_SMTP_USER=<EMAIL>
BREVO_SMTP_PASS=6rnTYQPSdMafC8Wq
BREVO_SEND_EMAIL=<EMAIL>
BREVO_API_KEY=xkeysib-9ac78f59c620393b66c53ef7c0d0b12f59c4b20ddb58c9a40f112ba2d96ac7-vw0bNZ2V2DccA7iK
STRIPE_SECRET_KEY=sk_test_51RU2HJPFPsuRFy0ndsdzUcivQi0Q2PR4DCjtyRBsloQSfCAI1COvf9O4PV4tCnjk93sOpsuQJa5x3NdybecqrWAa00IYq3WbZN
STRIPE_WEBHOOK_SECRET=whsec_9rCtrGn1a9YQrals5gpJWjgdErLwZSdv
```

### Step 3: Deploy

1. Push your code to GitHub
2. Railway will automatically detect the changes and deploy
3. Monitor the deployment logs for startup messages

### Step 4: Get Railway URL

1. Once deployed, Railway will provide a URL like: `https://your-app-name.up.railway.app`
2. Copy this URL for the next step

### Step 5: Update Frontend Environment

1. Go to Vercel Dashboard → Your Project → Settings → Environment Variables
2. Update `VITE_BACKEND_URL` to your Railway URL:
   ```
   VITE_BACKEND_URL=https://your-railway-url.up.railway.app
   ```
3. Redeploy your Vercel frontend

## 🧪 Testing Deployment

### Test Backend Health
```bash
curl https://your-railway-url.up.railway.app/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-26T...",
  "port": "PORT_NUMBER",
  "database": "connected",
  "environment": "production"
}
```

### Test Backend API
```bash
curl https://your-railway-url.up.railway.app/
```

### Test Frontend Connection
1. Visit https://guideway-consulting.vercel.app
2. Try logging in or accessing API-dependent features
3. Check browser console for any CORS or connection errors

## 🔧 Configuration Details

### Files Modified for Railway:
- `backend/server.js` - Updated CORS origins
- `backend/start.js` - Enhanced startup validation
- `backend/railway.json` - Deployment configuration
- `backend/package.json` - Updated start script
- `backend/.env.production` - Production environment template

### Key Features:
- ✅ Enhanced health check with MongoDB status
- ✅ Environment variable validation on startup
- ✅ CORS configured for Vercel frontend
- ✅ Graceful error handling
- ✅ Extended health check timeout (300s)
- ✅ Comprehensive logging

## 🚨 Troubleshooting

### Health Check Fails
1. Check Railway logs for startup errors
2. Verify all environment variables are set
3. Ensure MongoDB connection string is correct

### CORS Errors
1. Verify FRONTEND_URL is set correctly in Railway
2. Check that Vercel URL matches exactly
3. Clear browser cache and try again

### Database Connection Issues
1. Verify MongoDB Atlas allows connections from 0.0.0.0/0
2. Check connection string format
3. Ensure database user has proper permissions

### Frontend Can't Connect
1. Verify VITE_BACKEND_URL in Vercel matches Railway URL
2. Check Railway deployment status
3. Test backend health endpoint directly

## 📊 Monitoring

After deployment, monitor:
- Railway deployment logs
- Health check endpoint status
- Frontend console for errors
- Database connection stability

## ✅ Deployment Checklist

- [ ] Railway project created and connected to GitHub
- [ ] All environment variables set in Railway
- [ ] Code pushed to GitHub repository
- [ ] Railway deployment successful
- [ ] Health check returns 200 OK
- [ ] Railway URL obtained
- [ ] Vercel environment updated with Railway URL
- [ ] Frontend redeployed on Vercel
- [ ] End-to-end testing completed
- [ ] CORS working correctly
- [ ] Database operations functional

Your application should now be fully deployed and accessible at:
- **Frontend**: https://guideway-consulting.vercel.app
- **Backend**: https://your-railway-url.up.railway.app
