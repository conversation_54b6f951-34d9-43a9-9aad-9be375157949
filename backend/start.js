#!/usr/bin/env node

// Load environment variables first
require('dotenv').config();

// Startup script with enhanced logging for Railway deployment
console.log('🚀 Starting Guideway Consulting Backend...');
console.log('📊 Environment:', process.env.NODE_ENV || 'development');
console.log('🔌 Port:', process.env.PORT || '2340');
console.log('🌐 Host: 0.0.0.0 (Railway requirement)');
console.log('📦 MongoDB URL:', process.env.MONGO_URL ? 'Set ✅' : 'Missing ❌');
console.log('🔑 Secret Key:', process.env.SECRET_KEY ? 'Set ✅' : 'Missing ❌');
console.log('☁️ Cloudinary:', process.env.CLOUDINARY_CLOUD_NAME ? 'Set ✅' : 'Missing ❌');
console.log('📧 SMTP:', process.env.BREVO_SMTP_HOST ? 'Set ✅' : 'Missing ❌');
console.log('💳 Stripe:', process.env.STRIPE_SECRET_KEY ? 'Set ✅' : 'Missing ❌');
console.log('🎨 Frontend URL:', process.env.FRONTEND_URL || 'Not set');
console.log('');

// Check critical environment variables
const requiredEnvVars = [
  'MONGO_URL',
  'SECRET_KEY',
  'CLOUDINARY_CLOUD_NAME',
  'CLOUDINARY_API_KEY',
  'CLOUDINARY_API_SECRET'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => console.error(`   - ${varName}`));
  console.error('');
  console.error('🔧 Please set these variables in Railway dashboard');
  process.exit(1);
}

console.log('✅ All required environment variables are set');
console.log('🎯 Starting main server...');
console.log('');

// Start the main server
require('./server.js');
