// routes/brand/tracking.js
const express = require('express');
const router = express.Router();

// Controllers
const {
  addTrackingInfo,
  getTrackingForCreator
} = require('../../middlewares/brand/trackingController');
const { verifyBrandToken } = require('../../middlewares/brand/authController');

// Brand adds tracking info
router.post('/add', verifyBrandToken, addTrackingInfo);

// Creator views their tracking info
router.get('/creator/:campaignId', verifyBrandToken, getTrackingForCreator);

module.exports = router;
