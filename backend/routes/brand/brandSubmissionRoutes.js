const express = require("express");
const router = express.Router();
const { VerifyToken } = require("../../middlewares/auth");
const { verifyBrandToken } = require("../../middlewares/brand/authController");
const controller = require("../../middlewares/brand/brandSubmissionController");

// USER submission routes
router.post("/campaign-submission", VerifyToken, controller.createSubmission);
router.get("/campaign-submission/:campaignId", VerifyToken, controller.getUserSubmission);
router.put("/campaign-submission/:campaignId", VerifyToken, controller.updateSubmission);
router.delete("/campaign-submission/:campaignId", VerifyToken, controller.deleteSubmission);

// BRAND submission routes
router.get("/brand/submission/:campaignId/:email", verifyBrandToken, controller.getBrandSubmission);
router.post("/brand/submission/update-status", verifyBrandToken, controller.updateContentStatus);
router.post("/brand/submission/update-tracking", verifyBrandToken, controller.updateTrackingInfo);


module.exports = router;
