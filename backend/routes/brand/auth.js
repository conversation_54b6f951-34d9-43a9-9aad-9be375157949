// routes/brand/auth.js
const express = require('express');
const router  = express.Router();

const {
  signup,
  signin,
  verifyAuth,
  verifyBrandToken,
  forgotPassword,
} = require('../../middlewares/brand/authController');

// Brand signs up → triggers signup email, awaits admin approval
router.post('/signup', signup);

// Brand signs in → only once approved
router.post('/signin', signin);

// Verify token and ensure brand is approved
router.get('/verify', verifyBrandToken, verifyAuth);

// Add forgot password route
router.post('/forgot-password', forgotPassword);


module.exports = router;
