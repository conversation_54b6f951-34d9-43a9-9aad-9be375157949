const express = require('express');
const router = express.Router();
const { VerifyTokenAuth } = require('../../middlewares/AdminAuth');
const {
  getBrandsWithPlans,
  updateBrandPlan,
  getBrandPlanHistory
} = require('../../middlewares/admin/brandPlanController');

// Protect all routes with admin auth
router.use(VerifyTokenAuth);

// GET /api/admin/brands/plans - Get all brands with their plan information
router.get('/plans', getBrandsWithPlans);

// POST /api/admin/brands/:brandId/plan - Update a brand's plan
router.post('/:brandId/plan', updateBrandPlan);

// GET /api/admin/brands/:brandId/plan-history - Get plan history for a brand
router.get('/:brandId/plan-history', getBrandPlanHistory);

module.exports = router;
