const dotenv = require("dotenv");
const bcrypt = require("bcrypt");
const { User, RequestLog, referralReward  } = require("../database");
const axios = require("axios");
const {
  sendWelcomeEmail,
  sendVerificationLink,
  sendEmailWithTempPassword,
} = require("../functions/sendEmail");
const { generateToken, verifyToken } = require("../utils/jwtUtils");
const { OAuth2Client } = require("google-auth-library");

dotenv.config();

const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

// ✅ Middleware to protect private routes
async function VerifyToken(req, res, next) {
  const token = req.headers.authorization;

  try {
    const decoded = await verifyToken(token);
    const user = await User.findOne({ email: decoded.email });

    if (!user) {
      return res.json({ status: "failed", message: "User not found!" });
    }

    req.user = user;
    next();
  } catch {
    res.json({ status: "failed", message: "Invalid or expired token" });
  }
}

// ✅ Email/password login
async function signin(req, res) {
  const { email, password } = req.body;
  const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
  const userAgent = req.get("User-Agent");

  try {
    const user = await User.findOne({ email });

    await RequestLog.create({ email, ip, userAgent, action: "signin" });

    if (!user) return res.json({ status: "failed", message: "User not found" });
    if (user.blocked) return res.json({ status: "failed", message: "User is blocked" });
    if (user.isGoogleUser) return res.json({ status: "failed", message: "Please login using Google" });
    if (!user.isVerified) return res.json({ status: "failed", message: "Please verify your email first" });

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) return res.json({ status: "failed", message: "Incorrect password" });

    const token = generateToken({ email });
    res.json({ status: "success", message: "Login successful", token });
  } catch (error) {
    console.error("Signin error:", error.message);
    res.json({ status: "failed", message: "Something went wrong" });
  }
}

// ✅ Register new user
async function signup(req, res) {
  const { name, email, password, recaptchaToken, referrer } = req.body; // Optional referrer
  const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
  const userAgent = req.get("User-Agent");

  // 🔍 Basic input validation
  if (!name || !email || !password || !recaptchaToken) {
    return res.json({
      status: "failed",
      message: "Please provide name, email, password, and reCAPTCHA token",
    });
  }

  try {
    // 📜 Log the signup attempt
    await RequestLog.create({ email, ip, userAgent, action: "signup" });

    // 🛡️ Verify reCAPTCHA
    const RECAPTCHA_SECRET = process.env.RECAPTCHA_SECRET_KEY;
    const captchaRes = await axios.post(
      "https://www.google.com/recaptcha/api/siteverify",
      null,
      {
        params: {
          secret: RECAPTCHA_SECRET,
          response: recaptchaToken,
        },
      }
    );

    if (!captchaRes.data.success || (captchaRes.data.score && captchaRes.data.score < 0.7)) {
      return res.json({
        status: "failed",
        message: "reCAPTCHA verification failed. Are you a bot?",
      });
    }

    // 🛑 Check if user already exists
    const existing = await User.findOne({ email });
    if (existing) {
      return res.json({ status: "failed", message: "User already registered" });
    }

    // 🔐 Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // 👤 Create new user
    const newUser = new User({
      name,
      email,
      password: hashedPassword,
      isGoogleUser: false,
      isVerified: false,
      referrer: referrer || null, // optional field
    });

    await newUser.save();

    // 🔗 If referral is provided, update referrer and create reward
    if (referrer) {
      // 🧩 Add this new user to referrer's referral list
      await User.findByIdAndUpdate(referrer, {
        $push: { referrals: newUser._id },
      });

      // 🎁 Create referral reward entry only if referrer is present
      await referralReward.create({
        referrer: referrer,
        invitee: newUser._id,
        rewardGiven: false,
      });
    }

    // 📩 Send verification email
    const token = generateToken({ email });
    await sendVerificationLink(email, token);

    // ✅ All good
    return res.json({
      status: "success",
      message: "Signup successful. Verification link sent to your email.",
    });

  } catch (error) {
    // 🧨 Handle errors
    console.error("❌ Signup error:", error.message);
    return res.json({
      status: "failed",
      message: error.message || "Something went wrong during signup",
    });
  }
}


// ✅ FORGOT PASSWORD - Generate Temp Password and Email it
async function forgotPassword(req, res) {
  const { email } = req.body;
  const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
  const userAgent = req.get("User-Agent");

  if (!email) {
    return res.json({ status: "failed", message: "Email is required" });
  }

  try {
    const user = await User.findOne({ email: email.trim() });

    // ✅ Log request always
    await RequestLog.create({ email, ip, userAgent, action: "forgot-password" });

    if (!user || user.isGoogleUser) {
      // ✅ Generic response to avoid email detection
      return res.json({
        status: "success",
        message: "If this email exists, we've sent a temporary password.",
      });
    }

    // ✅ Create temporary password
    const tempPassword = Math.random().toString(36).slice(-8);
    const hashedTemp = await bcrypt.hash(tempPassword, 10);

    user.password = hashedTemp;
    await user.save();

    // ✅ Send email
    await sendEmailWithTempPassword(user.email, tempPassword, user.name);

    return res.json({
      status: "success",
      message: "If this email exists, we've sent a temporary password.",
    });
  } catch (err) {
    console.error("Forgot password error:", err.message);
    res.status(500).json({ status: "failed", message: "Something went wrong" });
  }
}

// ✅ Secure Google Login
async function googleAuth(req, res) {
  const { idToken, referrer } = req.body;
  const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
  const userAgent = req.get("User-Agent");

  if (!idToken) {
    return res.json({ status: "failed", message: "Missing Google ID token" });
  }

  try {
    const ticket = await client.verifyIdToken({
      idToken,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();

    if (!payload.email_verified) {
      return res.json({ status: "failed", message: "Google email not verified" });
    }

    const { email, name } = payload;

    await RequestLog.create({ email, ip, userAgent, action: "google-auth" });

    let user = await User.findOne({ email });

    // ✅ New Google user (first time signup)
    if (!user) {
      const hashedDummyPassword = await bcrypt.hash("googleuser", 10);
      user = new User({
        name,
        email,
        password: hashedDummyPassword,
        isGoogleUser: true,
        isVerified: true,
        referrer: referrer || null,
      });
      await user.save();

      // 🔁 Update referrer's referral array
      if (referrer) {
        await User.findByIdAndUpdate(referrer, {
          $push: { referrals: user._id },
        });

        // ✅ Create ReferralReward entry
        await referralReward.create({
          referrer,
          invitee: user._id,
          rewardGiven: false,
        });
      }

      if (!user.welcomeSent) {
  setImmediate(() => sendWelcomeEmail(email, name));
  user.welcomeSent = true;
  await user.save();
}

    }

    const token = generateToken({ email });
    res.json({ status: "success", message: "Google auth successful", token });

  } catch (error) {
    console.error("Google Auth Verification Failed:", error.message);
    res.json({ status: "failed", message: "Invalid or expired Google token" });
  }
}


// ✅ Verify email from link
async function verifyEmailToken(req, res) {
  const { token } = req.body;

  try {
    const decoded = await verifyToken(token);
    const user = await User.findOne({ email: decoded.email });

    if (!user) {
      return res.json({ status: "failed", message: "User not found" });
    }

    // ✅ Already verified
    if (user.isVerified) {
      return res.json({ status: "success", message: "Email already verified" });
    }

    // ✅ First time verification
    user.isVerified = true;

    // ✅ Only send welcome email if not sent before
    if (!user.welcomeSent) {
      setImmediate(() => sendWelcomeEmail(user.email, user.name));
      user.welcomeSent = true;
    }

    await user.save();

    return res.json({ status: "success", message: "Email verified successfully" });

  } catch (error) {
    return res.json({ status: "failed", message: "Invalid or expired token" });
  }
}


// ✅ Verify token route (for frontend)
async function verifyAuth(req, res) {
  const token = req.headers.authorization;

  try {
    const decoded = await verifyToken(token);
    const user = await User.findOne({ email: decoded.email });

    if (!user) return res.json({ status: "failed", message: "User not found!" });
    if (user.blocked) return res.json({ status: "failed", message: "User is blocked" });

    res.json({
      status: "success",
      message: "Verified",
      user: {
        name: user.name,
        email: user.email,
        instagramId: user.instagramId || "",
        youtubeId: user.youtubeId || "",
        tiktokId: user.tiktokId || "",
      },
    });
  } catch {
    res.json({ status: "failed", message: "Invalid or expired token" });
  }
}

module.exports = {
  VerifyToken,
  signin,
  signup,
  googleAuth,
  verifyAuth,
  verifyEmailToken,
  forgotPassword,
};
