/** @format */

// controllers/admin/brandApprovalController.js
const { Brand } = require('../database/index');
const {
  sendBrandApprovalEmail,
  sendBrandRejectionEmail
} = require('../functions/sendEmail');

/**
 * GET /api/admin/brands?status=pending|approved|all
 * List brands filtered by verification status
 */
async function getBrands(req, res) {
  try {
    const { status } = req.query;
    let filter = {};
    if (status === 'pending') filter.isVerified = false;
    else if (status === 'approved') filter.isVerified = true;

    const brands = await Brand.find(filter).sort({ createdAt: -1 });
    return res.json({ status: 'success', brands });
  } catch (error) {
    console.error('❌ getBrands error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching brands' });
  }
}

/**
 * PATCH /api/admin/brands/:id/approve
 * Approve a pending brand account
 */
async function approveBrand(req, res) {
  try {
    const { id } = req.params;
    const brand = await Brand.findById(id);
    if (!brand) return res.status(404).json({ status: 'failed', message: 'Brand not found' });
    if (brand.isVerified) return res.json({ status: 'success', message: 'Brand already approved', brand });

    brand.isVerified = true;
    await brand.save();
    await sendBrandApprovalEmail(brand.email, brand.companyName);
    return res.json({ status: 'success', brand });
  } catch (error) {
    console.error('❌ approveBrand error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error approving brand' });
  }
}

/**
 * PATCH /api/admin/brands/:id/reject
 * Reject and delete a pending brand account
 */
async function rejectBrand(req, res) {
  try {
    const { id } = req.params;
    const brand = await Brand.findById(id);
    if (!brand) return res.status(404).json({ status: 'failed', message: 'Brand not found' });

    await Brand.findByIdAndDelete(id);
    await sendBrandRejectionEmail(brand.email, brand.companyName);
    return res.json({ status: 'success', message: 'Brand rejected successfully' });
  } catch (error) {
    console.error('❌ rejectBrand error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error rejecting brand' });
  }
}

module.exports = {
  getBrands,
  approveBrand,
  rejectBrand
};