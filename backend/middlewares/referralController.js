// 📁 File: middlewares/referralController.js

const {
  User,
  referralReward,
  pointEventLog,
  pointRule,
  rewardTransaction,
  rewardTier,
} = require("../database");

const frontendUrl = process.env.FRONTEND_URL?.split(",")[0].trim();

// 🔹 Get referral dashboard data for user
async function getReferralDashboard(req, res) {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ status: "failed", message: "User not found" });
    }

    const rewards = await referralReward
      .find({ referrer: userId })
      .populate("invitee", "email")
      .lean();

    const completed = rewards.filter(r => r.rewardGiven).length;
    const progress = `${completed} / 3`;

    const referralBonus = 50;

    const table = rewards.map(r => ({
      email: r.invitee?.email || "(deleted user)",
      status: r.rewardGiven ? "Approved" : "Pending",
      points: r.rewardGiven ? referralBonus : 0,
    }));
    
    return res.json({
      status: "success",
      referralLink: `${frontendUrl}/signup?ref=${user._id}`,
      progress,
      table,
    });
  } catch (err) {
    console.error("❌ getReferralDashboard error:", err);
    return res.status(500).json({ status: "failed", message: "Internal error" });
  }
}

// 🔹 Get user point dashboard
async function getUserPoints(req, res) {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ status: "failed", message: "User not found" });
    }

    // Point logs
    const history = await pointEventLog
      .find({ user: userId })
      .sort({ createdAt: -1 })
      .lean();

    // All reward tiers
    const tiers = await rewardTier.find({}).sort({ pointsRequired: 1 });

    // ✅ Add redeemable tiers based on user.points
    const redeemable = tiers.filter(tier => user.points >= tier.pointsRequired);

    return res.json({
      status: "success",
      points: user.points,
      progress: tiers,
      redeemable, // ✅ Send only tiers user is eligible for
      history,
    });
  } catch (err) {
    console.error("❌ getUserPoints error:", err);
    return res.status(500).json({ status: "failed", message: "Internal error" });
  }
}


// 🔹 Redeem reward request
async function requestReward(req, res) {
  try {
    const userId = req.user._id;
    const { tierId } = req.body;

    const tier = await rewardTier.findById(tierId);
    if (!tier) {
      return res.status(400).json({ status: "failed", message: "Invalid tier" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ status: "failed", message: "User not found" });
    }

    if (user.points < tier.points) {
      return res.status(400).json({ status: "failed", message: "Not enough points to redeem this reward" });
    }

    user.points -= tier.points;
    await user.save();

    await rewardTransaction.create({
      user: userId,
      points: tier.points,
      reward: tier.reward,
      status: "Pending", // Admin will fulfill this
    });

    return res.json({
      status: "success",
      message: "Reward requested! You'll receive your gift card soon.",
    });
  } catch (err) {
    console.error("❌ requestReward error:", err);
    return res.status(500).json({ status: "failed", message: "Server error" });
  }
}

// 🔹 Helper to fetch point value from DB rules
// 🔹 FIXED: Helper to fetch point value from DB rules
async function getPointValue(type) {
  const rule = await pointRule.findOne({ type });
  return rule?.value || 0;
}


module.exports = {
  getReferralDashboard,
  getUserPoints,
  requestReward,
};
    