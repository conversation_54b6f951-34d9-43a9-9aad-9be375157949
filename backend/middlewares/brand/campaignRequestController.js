const { BrandCampaignRequest, BrandSubscription } = require('../../database/index');

/**
 * Create a new campaign request (Brand side)
 */
async function requestNewCampaign(req, res) {
  try {
    const brandId = req.user.id;
    const data = req.body;

    // 1) Verify active subscription and campaign limit (including add-ons)
    const subscription = await BrandSubscription.findOne({ brand: brandId, status: 'active' }).populate('plan');
    if (!subscription) {
      return res.status(403).json({ status: 'failed', message: 'No active subscription found' });
    }
    const totalAllowed = (subscription.plan.campaignsAllowed || 0) + (subscription.extraCampaignsAllowed || 0);
    if (subscription.campaignsUsed >= totalAllowed) {
      return res.status(403).json({ status: 'failed', message: 'Campaign limit reached' });
    }

    // 2) Create request
    const request = await BrandCampaignRequest.create({ ...data, brand: brandId });

    // 3) Increment used campaigns count
    subscription.campaignsUsed += 1;
    await subscription.save();

    return res.status(201).json({ status: 'success', request });
  } catch (err) {
    console.error('❌ requestNewCampaign error:', err);
    return res.status(500).json({ status: 'failed', message: 'Server error creating campaign request' });
  }
}

/**
 * Get all campaign requests for the authenticated brand
 */
async function getMyCampaignRequests(req, res) {
  try {
    const brandId = req.user.id;
    const requests = await BrandCampaignRequest.find({ brand: brandId }).sort({ createdAt: -1 });
    return res.json({ status: 'success', requests });
  } catch (err) {
    console.error('❌ getMyCampaignRequests error:', err);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching campaign requests' });
  }
}

/**
 * Get a single campaign request by ID (Brand side)
 */
async function getCampaignRequestById(req, res) {
  try {
    const brandId = req.user.id;
    const { id } = req.params;
    const request = await BrandCampaignRequest.findOne({ _id: id, brand: brandId });
    if (!request) {
      return res.status(404).json({ status: 'failed', message: 'Request not found' });
    }
    return res.json({ status: 'success', request });
  } catch (err) {
    console.error('❌ getCampaignRequestById error:', err);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching request' });
  }
}

/**
 * Update a pending campaign request (Brand side)
 */
async function updateCampaignRequest(req, res) {
  try {
    const brandId = req.user.id;
    const { id } = req.params;
    const data = req.body;

    // Only allow update if request is pending
    const request = await BrandCampaignRequest.findOne({ _id: id, brand: brandId, approvalStatus: 'Pending' });
    if (!request) {
      return res.status(404).json({ status: 'failed', message: 'Request not found or cannot be updated' });
    }

    Object.assign(request, data);
    await request.save();
    return res.json({ status: 'success', request });
  } catch (err) {
    console.error('❌ updateCampaignRequest error:', err);
    return res.status(500).json({ status: 'failed', message: 'Server error updating campaign request' });
  }
}

/**
 * Delete (withdraw) a pending campaign request (Brand side)
 */
async function deleteCampaignRequest(req, res) {
  try {
    const brandId = req.user.id;
    const { id } = req.params;

    // Delete pending request
    const deleted = await BrandCampaignRequest.findOneAndDelete({ _id: id, brand: brandId, approvalStatus: 'Pending' });
    if (!deleted) {
      return res.status(404).json({ status: 'failed', message: 'Request not found or cannot be deleted' });
    }

    // Decrement used campaigns count on subscription
    const subscription = await BrandSubscription.findOne({ brand: brandId, status: 'active' });
    if (subscription) {
      subscription.campaignsUsed = Math.max((subscription.campaignsUsed || 1) - 1, 0);
      await subscription.save();
    }

    return res.json({ status: 'success', message: 'Campaign request withdrawn successfully' });
  } catch (err) {
    console.error('❌ deleteCampaignRequest error:', err);
    return res.status(500).json({ status: 'failed', message: 'Server error deleting campaign request' });
  }
}

module.exports = {
  requestNewCampaign,
  getMyCampaignRequests,
  getCampaignRequestById,
  updateCampaignRequest,
  deleteCampaignRequest,
};
