const mongoose = require('mongoose');
const { appliedCampaigns, Campaign, BrandCampaignRequest } = require('../../database/index');
const { handleFirstContentApproval } = require('../../services/pointService');
const { sendContentSubmissionReminderEmail } = require('../../functions/sendEmail');

module.exports = {
  // ✅ GET /brand/campaign-apply/:id — where ID is brandcampaignrequests _id
  async getApplications(req, res) {
    try {
      const limit = 100;
      const cursor = req.query.cursor;
      const { id } = req.params;

      console.log("📥 [GET] /brand/campaign-apply/:id - Received ID:", id);

      // Step 1: Validate ID format
      if (!mongoose.Types.ObjectId.isValid(id)) {
        console.warn("❌ Invalid campaign request ID format:", id);
        return res.status(400).json({ status: 'failed', message: 'Invalid campaign request ID format' });
      }

      // Step 2: Find the original brand campaign request
      const request = await BrandCampaignRequest.findById(id);
      if (!request) {
        console.warn("❌ Brand campaign request not found for ID:", id);
        return res.status(404).json({ status: 'failed', message: 'Brand campaign request not found' });
      }

      // Step 3: Find the live campaign using referenceId
      const campaign = await Campaign.findOne({ referenceId: request._id });
      if (!campaign) {
        console.warn("❌ Live campaign not found for referenceId:", request._id);
        return res.status(404).json({ status: 'failed', message: 'Live campaign not found for this request' });
      }

      // Step 4: Prepare query for fetching applications
      const query = { campaign: campaign._id };
      if (cursor) query._id = { $gt: cursor };

      const results = await appliedCampaigns.find(query).sort({ _id: 1 }).limit(limit + 1);
      const hasMore = results.length > limit;
      const limitedResults = hasMore ? results.slice(0, limit) : results;

      // Step 5: Format application data
      const applications = limitedResults.map((item) => ({
        id: item._id,
        name: item.name,
        email: item.email,
        phone: item.phone,
        address: `${item.address}, ${item.city}, ${item.state}, ${item.zipCode}`,
        appliedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(), // timezone offset
        status: item.status,
        rejectionReason: item.rejectionReason,
        showReasonToInfluencer: item.showReasonToInfluencer,
      }));

      // Step 6: Count approved applications
      const approvedCount = await appliedCampaigns.countDocuments({ campaign: campaign._id, status: 'Approved' });

      // Step 7: Send response
      console.log("✅ Applications fetched:", applications.length);
      res.json({
        status: 'success',
        applications,
        nextCursor: hasMore ? limitedResults[limitedResults.length - 1]._id : null,
        isLastPage: !hasMore,
        campaignTitle: campaign.campaignTitle,
        recruitingLimit: campaign.recruiting,
        approvedCount,
      });
    } catch (error) {
      console.error('❌ Error fetching applications:', error);
      res.status(500).json({ message: 'Something went wrong', error });
    }
  },

  // PATCH /brand/campaign-apply/:applicationId/status
  async updateApplicationStatus(req, res) {
    const { applicationId } = req.params;
    const { status, rejectionReason, showReasonToInfluencer } = req.body;

    console.log("📥 [PATCH] /brand/campaign-apply/:applicationId/status", applicationId);
    console.log("📝 Status:", status);

    if (!['Approved', 'Rejected', 'Pending'].includes(status)) {
      return res.status(400).json({ status: 'failed', message: 'Invalid status value' });
    }

    try {
      const application = await appliedCampaigns.findById(applicationId);
      if (!application) {
        console.warn("❌ Application not found:", applicationId);
        return res.status(404).json({ status: 'failed', message: 'Application not found' });
      }

      let campaign = null;
      if (status === 'Approved') {
        campaign = await Campaign.findById(application.campaign);
        if (!campaign) {
          console.warn("❌ Campaign not found for application:", applicationId);
          return res.status(404).json({ status: 'failed', message: 'Associated campaign not found' });
        }

        const approvedCount = await appliedCampaigns.countDocuments({
          campaign: campaign._id,
          status: 'Approved',
        });

        if (approvedCount >= campaign.recruiting) {
          return res.status(409).json({
            status: 'failed',
            message: `Recruiting limit full. ${approvedCount} / ${campaign.recruiting} seats filled.`,
          });
        }
      }

      application.status = status;
      if (status === 'Rejected') {
        application.rejectionReason = rejectionReason || '';
        application.showReasonToInfluencer = !!showReasonToInfluencer;
      } else {
        application.rejectionReason = '';
        application.showReasonToInfluencer = false;
      }

      await application.save();

      if (status === 'Approved') {
        await handleFirstContentApproval(application.user, application.campaign);
        await sendContentSubmissionReminderEmail(application.email, application.name, campaign.campaignTitle);
        console.log("📧 Approval email sent to:", application.email);
      }

      return res.status(200).json({
        status: 'success',
        message: `Application marked as ${status}`,
        application,
      });
    } catch (err) {
      console.error('❌ Error updating application status:', err);
      return res.status(500).json({ status: 'failed', message: 'Server error' });
    }
  },

  // DELETE /brand/campaign-apply/:campaignId/:applicantId
  async deleteApplication(req, res) {
    try {
      const { applicantId } = req.params;
      console.log("🗑️ Deleting applicant:", applicantId);

      const deletedApplicant = await appliedCampaigns.findByIdAndDelete(applicantId);

      if (!deletedApplicant) {
        return res.status(404).json({ status: 'error', message: 'Applicant not found' });
      }

      res.status(200).json({ status: 'success', message: 'Applicant deleted successfully' });
    } catch (error) {
      console.error('❌ Error deleting applicant:', error);
      res.status(500).json({ status: 'error', message: 'Server error while deleting applicant' });
    }
  }
};
