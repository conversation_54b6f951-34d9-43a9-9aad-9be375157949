const { BrandSubscription, BrandCampaignRequest } = require('../../database/index');

async function getDashboardData(req, res) {
  try {
    const brandId = req.user.id;

    // 1. Find active subscription
    const sub = await BrandSubscription
      .findOne({ brand: brandId, status: 'active' })
      .populate('plan');

    if (!sub) {
      return res.status(404).json({ status: 'failed', message: 'No active subscription found' });
    }

    // 2. Count approved brand campaigns (instead of trusting stored campaignsUsed)
    const campaignsUsed = await BrandCampaignRequest.countDocuments({
      brand: brandId,
      status: 'approved'
    });

    // 3. Extract plan info
    const {
      upgradeEligibleTill,
      expiresAt
    } = sub;

    const {
      campaignsAllowed,
      name: planName
    } = sub.plan;

    const remainingCount = campaignsAllowed - campaignsUsed;
    const usagePercentage = Math.round((campaignsUsed / campaignsAllowed) * 100);

    return res.json({
      status: 'success',
      data: {
        planName,
        campaignsAllowed,
        campaignsUsed,
        remainingCount,
        usagePercentage,
        expiresAt,
        upgradeEligibleTill
      }
    });
  } catch (error) {
    console.error('❌ getDashboardData error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching dashboard data' });
  }
}

module.exports = { getDashboardData };
