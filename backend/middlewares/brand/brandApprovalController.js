// controllers/admin/brandApprovalController.js
const { Brand } = require('../../database/index');
const {
  sendBrandApprovalEmail,
  sendBrandRejectionEmail
} = require('../../functions/sendEmail');

/**
 * GET /admin/brands/pending
 * Retrieve all pending brand sign-up requests
 */
async function getPendingBrands(req, res) {
  try {
    const pending = await Brand.find({ isVerified: false });
    return res.json({ status: 'success', pending });
  } catch (error) {
    console.error('❌ getPendingBrands error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching pending brands' });
  }
}

/**
 * PATCH /admin/brands/:id/approve
 * Approve a pending brand account
 */
async function approveBrand(req, res) {
  try {
    const { id } = req.params;
    const brand = await Brand.findById(id);
    if (!brand) {
      return res.status(404).json({ status: 'failed', message: 'Brand not found' });
    }

    if (brand.isVerified) {
      return res.json({ status: 'success', message: 'Brand already approved', brand });
    }

    brand.isVerified = true;
    await brand.save();

    // Send approval email
    await sendBrandApprovalEmail(brand.email, brand.companyName);

    return res.json({ status: 'success', brand });
  } catch (error) {
    console.error('❌ approveBrand error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error approving brand' });
  }
}

/**
 * PATCH /admin/brands/:id/reject
 * Reject and delete a pending brand account
 */
async function rejectBrand(req, res) {
  try {
    const { id } = req.params;
    const brand = await Brand.findById(id);
    if (!brand) {
      return res.status(404).json({ status: 'failed', message: 'Brand not found' });
    }

    // Delete brand
    await Brand.findByIdAndDelete(id);

    // Send rejection email
    await sendBrandRejectionEmail(brand.email, brand.companyName);

    return res.json({ status: 'success', message: 'Brand rejected successfully' });
  } catch (error) {
    console.error('❌ rejectBrand error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error rejecting brand' });
  }
}

module.exports = {
  getPendingBrands,
  approveBrand,
  rejectBrand
};
