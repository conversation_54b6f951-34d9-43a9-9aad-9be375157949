const { BrandPlan } = require('../../database/index');

/**
 * GET /brand/package/plans
 * Public: Retrieve all active plans
 */
async function getAllPlans(req, res) {
  try {
    // Returns all plan fields, including add-on pricing and validity
    const plans = await BrandPlan.find({ isActive: true });
    return res.json({ status: 'success', plans });
  } catch (error) {
    console.error('❌ getAllPlans error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching plans' });
  }
}

/**
 * POST /brand/package/plans
 * Admin only: Create a new plan
 */
async function createPlan(req, res) {
  try {
    const {
      name,
      price,
      type,
      campaignsAllowed,
      creatorsAllowed,
      validityMonths,
      tag,
      isActive,
      campaignAddonPrice,
      creatorAddonPrice
    } = req.body;

    // Validate required fields
    if (
      !name ||
      typeof price !== 'number' ||
      typeof campaignsAllowed !== 'number' ||
      typeof creatorsAllowed !== 'number' ||
      typeof validityMonths !== 'number' ||
      (campaignAddonPrice !== undefined && typeof campaignAddonPrice !== 'number') ||
      (creatorAddonPrice !== undefined && typeof creatorAddonPrice !== 'number')
    ) {
      return res.status(400).json({ status: 'failed', message: 'Missing or invalid plan fields' });
    }

    const plan = await BrandPlan.create({
      name,
      price,
      type,
      campaignsAllowed,
      creatorsAllowed,
      validityMonths,
      tag: tag || '',
      isActive: isActive !== false,
      campaignAddonPrice: campaignAddonPrice || 0,
      creatorAddonPrice: creatorAddonPrice || 0
    });

    return res.status(201).json({ status: 'success', plan });
  } catch (error) {
    console.error('❌ createPlan error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error creating plan' });
  }
}

/**
 * PATCH /brand/package/plans/:id
 * Admin only: Update an existing plan
 */
async function updatePlan(req, res) {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Validate numeric fields if provided
    if (
      (updates.price !== undefined && typeof updates.price !== 'number') ||
      (updates.campaignsAllowed !== undefined && typeof updates.campaignsAllowed !== 'number') ||
      (updates.creatorsAllowed !== undefined && typeof updates.creatorsAllowed !== 'number') ||
      (updates.validityMonths !== undefined && typeof updates.validityMonths !== 'number') ||
      (updates.campaignAddonPrice !== undefined && typeof updates.campaignAddonPrice !== 'number') ||
      (updates.creatorAddonPrice !== undefined && typeof updates.creatorAddonPrice !== 'number')
    ) {
      return res.status(400).json({ status: 'failed', message: 'Invalid field values' });
    }

    const plan = await BrandPlan.findByIdAndUpdate(id, updates, { new: true });
    if (!plan) {
      return res.status(404).json({ status: 'failed', message: 'Plan not found' });
    }
    return res.json({ status: 'success', plan });
  } catch (error) {
    console.error('❌ updatePlan error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error updating plan' });
  }
}

/**
 * DELETE /brand/package/plans/:id
 * Admin only: Soft-delete a plan (mark inactive)
 */
async function deletePlan(req, res) {
  try {
    const { id } = req.params;
    const plan = await BrandPlan.findByIdAndUpdate(
      id,
      { isActive: false },
      { new: true }
    );
    if (!plan) {
      return res.status(404).json({ status: 'failed', message: 'Plan not found' });
    }
    return res.json({ status: 'success', message: 'Plan deactivated', plan });
  } catch (error) {
    console.error('❌ deletePlan error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error deleting plan' });
  }
}

module.exports = {
  getAllPlans,
  createPlan,
  updatePlan,
  deletePlan
};
