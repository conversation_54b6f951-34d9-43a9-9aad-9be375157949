const mongoose = require("mongoose");
const { appliedCampaigns, campaignSubmission, User, Campaign } = require("../../database/index");
const { handleFirstContentApproval } = require("../../services/pointService");
const { sendTrackingEmailToUser } = require("../../functions/sendEmail");

// --- Create Submission
exports.createSubmission = async (req, res) => {
  const user_id = req.user._id;
  const { campaign_id, instagram_urls, tiktok_urls, allow_brand_reuse } = req.body;

  try {
    const application = await appliedCampaigns.findOne({
      campaign: campaign_id,
      email: req.user.email,
      status: "Approved",
    });

    if (!application) {
      return res.status(403).json({ status: "failed", message: "Not approved for this campaign." });
    }

    const submission = await campaignSubmission.create({
      campaign_id,
      user_id,
      email: req.user.email,
      instagram_urls,
      tiktok_urls,
      allow_brand_reuse,
    });

    return res.status(201).json({ status: "success", message: "Submission saved.", submission });
  } catch (error) {
    console.error("Create submission error:", error);
    return res.status(500).json({ status: "failed", message: "Internal server error" });
  }
};

// --- Get User Submission
exports.getUserSubmission = async (req, res) => {
  const user_id = req.user._id;
  const campaign_id = req.params.campaignId;

  try {
    const submission = await campaignSubmission.findOne({ campaign_id, user_id });
    if (!submission) {
      return res.status(404).json({ status: "failed", message: "No submission found" });
    }

    return res.json({
      status: "success",
      data: {
        instagram_urls: submission.instagram_urls,
        tiktok_urls: submission.tiktok_urls,
        allow_brand_reuse: submission.allow_brand_reuse,
        status: submission.status,
        submitted_at: submission.submitted_at,
      },
    });
  } catch (error) {
    console.error("Get submission error:", error);
    return res.status(500).json({ status: "failed", message: "Server error" });
  }
};

// --- Update Submission
exports.updateSubmission = async (req, res) => {
  const user_id = req.user._id;
  const campaign_id = req.params.campaignId;
  const { instagram_urls, tiktok_urls, allow_brand_reuse } = req.body;

  try {
    const submission = await campaignSubmission.findOne({ campaign_id, user_id });
    if (!submission) {
      return res.status(404).json({ status: "failed", message: "Submission not found" });
    }

    submission.instagram_urls = instagram_urls ?? submission.instagram_urls;
    submission.tiktok_urls = tiktok_urls ?? submission.tiktok_urls;
    submission.allow_brand_reuse = allow_brand_reuse ?? submission.allow_brand_reuse;

    await submission.save();

    return res.json({ status: "success", message: "Submission updated", submission });
  } catch (error) {
    console.error("Update error:", error);
    return res.status(500).json({ status: "failed", message: "Update failed" });
  }
};

// --- Delete Submission
exports.deleteSubmission = async (req, res) => {
  const user_id = req.user._id;
  const campaign_id = req.params.campaignId;

  try {
    const result = await campaignSubmission.deleteOne({ campaign_id, user_id });
    if (result.deletedCount === 0) {
      return res.status(404).json({ status: "failed", message: "Nothing to delete" });
    }

    return res.json({ status: "success", message: "Submission deleted" });
  } catch (error) {
    console.error("Delete error:", error);
    return res.status(500).json({ status: "failed", message: "Server error" });
  }
};

// --- Brand Get Submission
exports.getBrandSubmission = async (req, res) => {
  const { campaignId, email } = req.params;

  try {
    // Step 1: Find campaign in campaigns collection using referenceId
    const realCampaign = await Campaign.findOne({
  referenceId: campaignId,
});


    if (!realCampaign) {
      return res.status(404).json({
        status: "failed",
        message: "Referenced campaign not found in campaigns collection",
      });
    }

    // Step 2: Use real campaign ID to fetch submission
    const submission = await campaignSubmission.findOne({
      campaign_id: realCampaign._id,
      email: decodeURIComponent(email).toLowerCase(),
    });

    if (!submission) {
      return res.status(404).json({
        status: "failed",
        message: "No submission found for this campaign and email",
      });
    }

    return res.json({
      status: "success",
      data: {
        instagram_urls: submission.instagram_urls,
        tiktok_urls: submission.tiktok_urls,
        allow_brand_reuse: submission.allow_brand_reuse,
        status: submission.status,
        submitted_at: submission.submitted_at,
        content_status: submission.content_status || {},
        tracking_info: submission.tracking_info || {}, // ✅ ADD THIS LINE

      },
    });
  } catch (error) {
    console.error("Brand fetch error:", error);
    return res.status(500).json({
      status: "failed",
      message: "Server error while fetching submission",
    });
  }
};

// --- Brand Update Submission Status
exports.updateContentStatus = async (req, res) => {
  const { campaignId, email, contentStatus } = req.body;

  if (!campaignId || !email || !contentStatus?.content_status) {
    return res.status(400).json({
      status: "failed",
      message: "Missing campaignId, email, or content_status",
    });
  }

  if (!mongoose.Types.ObjectId.isValid(campaignId)) {
    return res.status(400).json({ status: "failed", message: "Invalid campaignId" });
  }

  try {
    const submission = await campaignSubmission.findOne({
      campaign_id: new mongoose.Types.ObjectId(campaignId),
      email: email.toLowerCase(),
    });

    if (!submission) {
      return res.status(404).json({ status: "failed", message: "Submission not found" });
    }

    submission.content_status = contentStatus.content_status;

    if (submission.status === "submitted" && contentStatus.content_status !== "Pending") {
      submission.status = "reviewed";
    }

    await submission.save();

    if (submission.status === "reviewed" && contentStatus.content_status === "Approved") {
      const user = await User.findOne({ email: email.toLowerCase() });
      if (user) {
        await handleFirstContentApproval(user._id, campaignId);
      }
    }

    return res.status(200).json({
      status: "success",
      message: "Submission content status updated",
      data: {
        workflow_status: submission.status,
        content_status: submission.content_status,
      },
    });
  } catch (error) {
    console.error("Update status error:", error);
    return res.status(500).json({ status: "failed", message: "Update failed" });
  }
};

exports.updateTrackingInfo = async (req, res) => {
  const { campaignId, email, courier, tracking_number } = req.body;

  if (!campaignId || !email || !tracking_number) {
    return res.status(400).json({
      status: "failed",
      message: "Missing campaignId, email, or tracking_number",
    });
  }

  try {
    // 🧠 Step 1: Find real campaign by referenceId
    const realCampaign = await Campaign.findOne({ referenceId: campaignId });

    if (!realCampaign) {
      return res.status(404).json({
        status: "failed",
        message: "Referenced campaign not found",
      });
    }

    // 🧠 Step 2: Find submission by real _id and email
    const submission = await campaignSubmission.findOne({
      campaign_id: realCampaign._id,
      email: email.toLowerCase(),
    });

    if (!submission) {
      return res.status(404).json({
        status: "failed",
        message: "Submission not found for this campaign and email",
      });
    }

    const prevTracking = submission.tracking_info?.tracking_number || "";

    const tracking_link = `https://www.17track.net/en/track?nums=${tracking_number}`;

    submission.tracking_info = {
      courier,
      tracking_number,
      tracking_link,
      last_updated: new Date(),
    };

    await submission.save();

    // 📧 Email only if tracking updated
    if (!prevTracking || prevTracking !== tracking_number) {
      const user = await User.findOne({ email: email.toLowerCase() });
      if (user) {
        await sendTrackingEmailToUser(user.email, tracking_link);
      }
    }

    return res.status(200).json({
      status: "success",
      message: "Tracking info updated",
      data: submission.tracking_info,
    });
  } catch (err) {
    console.error("❌ updateTrackingInfo error:", err);
    return res.status(500).json({
      status: "failed",
      message: "Server error while updating tracking info",
    });
  }
};
