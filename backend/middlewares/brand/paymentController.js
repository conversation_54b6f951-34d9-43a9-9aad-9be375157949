// controllers/brand/paymentController.js
const { PaymentHistory } = require('../../database/index');

async function getMyPaymentHistory(req, res) {
  try {
    // Fetch and populate plan info
    const rawHistory = await PaymentHistory
      .find({ brand: req.user.id })
      .populate('plan')
      .sort({ createdAt: -1 })
      .lean();  // get plain JS objects

    // Format each entry, pulling out addon metadata when present
    const history = rawHistory.map(item => {
      const entry = {
        id:         item._id,
        date:       item.createdAt,
        type:       item.type,            // 'purchase' | 'upgrade' | 'addon'
        planName:   item.plan.name,
        amountPaid: item.amountPaid,
      };

      if (item.type === 'addon' && item.metadata) {
        entry.addonType = item.metadata.addonType;   // 'campaign' or 'creator'
        entry.quantity  = Number(item.metadata.quantity);
      }

      return entry;
    });

    return res.json({ status: "success", history });
  } catch (err) {
    console.error("❌ Error fetching brand payment history:", err);
    return res.status(500).json({ status: "failed", message: "Something went wrong" });
  }
}

module.exports = { getMyPaymentHistory };
