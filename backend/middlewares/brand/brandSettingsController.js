const { Brand } = require('../../database/index');
const bcrypt    = require('bcrypt');
const fs        = require('fs');
const cloudinary = require('cloudinary').v2;


exports.getBrandSettings = async (req, res) => {
  try {
    const brand = await Brand
      .findById(req.user._id)
      .select('-password');
    return res.json({ status: 'success', brand });
  } catch (err) {
    console.error('Error fetching settings:', err);
    return res
      .status(500)
      .json({ status: 'failed', message: 'Server error' });
  }
};

exports.updateBrandSettings = async (req, res) => {
  try {
    const updates = {};

    // 1) Text fields
    [
      'companyName',
      'contactName',
      'email',
      'phone',
      'website',
      'productCategory',
      'introduction'
    ].forEach(f => {
      if (req.body[f] != null) updates[f] = req.body[f];
    });

    // 2) Logo upload → Cloudinary
    if (req.file) {
      const result = await cloudinary.uploader.upload(req.file.path, {
        folder: 'brand_logos'
      });
      updates.logo = result.secure_url;
      // remove temp file
      fs.unlink(req.file.path, () => {});
    }

    // 3) Password change (optional)
    const { newPassword, confirmPassword } = req.body;
    if (newPassword || confirmPassword) {
      if (!newPassword || newPassword !== confirmPassword) {
        return res
          .status(400)
          .json({ status: 'failed', message: 'Passwords must match' });
      }
      updates.password = await bcrypt.hash(newPassword, 10);
    }

    // 4) Apply update
    const updated = await Brand.findByIdAndUpdate(
      req.user._id,
      updates,
      { new: true, runValidators: true }
    ).select('-password');

    return res.json({ status: 'success', brand: updated });
  } catch (err) {
    console.error('Error updating settings:', err);
    return res
      .status(500)
      .json({ status: 'failed', message: 'Server error' });
  }
};
