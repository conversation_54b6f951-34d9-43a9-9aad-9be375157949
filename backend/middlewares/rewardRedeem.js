// ✅ File: middlewares/rewardRedeem.js

const { User, rewardTier, rewardTransaction, pointEventLog } = require("../database");
const { sendRedemptionEmailToAdmin } = require("../functions/sendEmail");

// Redeem reward
async function redeemReward(req, res) {
  try {
    const userId = req.user._id;
    const { tierId } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ status: "failed", message: "User not found" });
    }

    const tier = await rewardTier.findById(tierId);
    if (!tier || !tier.active) {
      return res.status(400).json({ status: "failed", message: "Invalid reward tier" });
    }

    if (user.points < tier.pointsRequired) {
      return res.status(400).json({ status: "failed", message: "Not enough points to redeem this reward" });
    }

    // Deduct points
    user.points -= tier.pointsRequired;
    await user.save();

    // Create transaction
    const transaction = await rewardTransaction.create({
      user: userId,
      rewardTier: tier._id,
      status: "pending",
    });

    // Log event
    await pointEventLog.create({
      user: userId,
      source: "redeem",
      points: -tier.pointsRequired,
      note: `${tier.rewardLabel} redeemed`,
    });

    // Email admin
    await sendRedemptionEmailToAdmin({
      userEmail: user.email,
      userName: user.name,
      rewardLabel: tier.rewardLabel,
      pointsUsed: tier.pointsRequired,
    });

    return res.json({
      status: "success",
      message: `You have successfully redeemed ${tier.rewardLabel}`,
      transactionId: transaction._id,
    });
  } catch (err) {
    console.error("❌ Redemption error:", err);
    return res.status(500).json({ status: "failed", message: "Server error" });
  }
}


module.exports = {
  redeemReward,
};
