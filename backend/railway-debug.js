#!/usr/bin/env node

// Railway Debug Script - Run this to test environment before deployment
console.log('🔍 Railway Deployment Debug Information');
console.log('=====================================');
console.log('');

console.log('📊 Environment Variables:');
console.log('NODE_ENV:', process.env.NODE_ENV || 'Not set');
console.log('PORT:', process.env.PORT || 'Not set (will use 2340)');
console.log('MONGO_URL:', process.env.MONGO_URL ? 'Set ✅' : 'Missing ❌');
console.log('SECRET_KEY:', process.env.SECRET_KEY ? 'Set ✅' : 'Missing ❌');
console.log('FRONTEND_URL:', process.env.FRONTEND_URL || 'Not set');
console.log('CLOUDINARY_CLOUD_NAME:', process.env.CLOUDINARY_CLOUD_NAME ? 'Set ✅' : 'Missing ❌');
console.log('CLOUDINARY_API_KEY:', process.env.CLOUDINARY_API_KEY ? 'Set ✅' : 'Missing ❌');
console.log('CLOUDINARY_API_SECRET:', process.env.CLOUDINARY_API_SECRET ? 'Set ✅' : 'Missing ❌');
console.log('');

console.log('🔧 System Information:');
console.log('Node.js Version:', process.version);
console.log('Platform:', process.platform);
console.log('Architecture:', process.arch);
console.log('Working Directory:', process.cwd());
console.log('');

console.log('📦 Package Information:');
try {
  const packageJson = require('./package.json');
  console.log('App Name:', packageJson.name);
  console.log('App Version:', packageJson.version);
  console.log('Start Script:', packageJson.scripts.start);
} catch (error) {
  console.log('❌ Could not read package.json:', error.message);
}
console.log('');

console.log('🌐 Network Configuration:');
console.log('Expected Host: 0.0.0.0');
console.log('Expected Port:', process.env.PORT || '2340');
console.log('Health Check Path: /health');
console.log('');

console.log('🔗 MongoDB Connection Test:');
if (process.env.MONGO_URL) {
  const mongoose = require('mongoose');
  
  console.log('Testing MongoDB connection...');
  mongoose.connect(process.env.MONGO_URL, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    serverSelectionTimeoutMS: 5000,
  })
  .then(() => {
    console.log('✅ MongoDB connection successful');
    console.log('Database:', mongoose.connection.db.databaseName);
    mongoose.connection.close();
  })
  .catch((error) => {
    console.log('❌ MongoDB connection failed:', error.message);
  });
} else {
  console.log('❌ MONGO_URL not set, skipping connection test');
}

console.log('');
console.log('🚀 Ready for Railway deployment!');
console.log('Make sure all environment variables are set in Railway dashboard.');
