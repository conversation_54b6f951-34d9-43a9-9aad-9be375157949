const dotenv = require('dotenv');
dotenv.config();
const { fetch17TrackStatus } = require('./services/cronService');

const trackingNumber = process.argv[2] || 'LB123456789US';

(async () => {
  console.log('Testing 17TRACK API with tracking number:', trackingNumber);
  const result = await fetch17TrackStatus(trackingNumber);
  console.log('17TRACK API result:', JSON.stringify(result, null, 2));
  process.exit(0);
})(); 