# Use the official Node.js image as the base image
FROM node:20-alpine

# Set the working directory inside the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json first (better for Docker caching)
COPY package*.json ./

# Install dependencies using npm install for better Railway compatibility
RUN npm install --only=production && npm cache clean --force

# Copy the rest of the application code
COPY . .

# Create uploads folder with proper permissions
RUN mkdir -p /usr/src/app/uploads && chmod 755 /usr/src/app/uploads

# Railway will set the PORT environment variable dynamically
# No need to expose a specific port

# Railway handles health checks externally

# Command to start the application
CMD ["npm", "run", "start"]
