{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node start.js", "dev": "nodemon server.js", "debug": "node railway-debug.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "bcrypt": "^5.1.1", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-fileupload": "^1.5.1", "express-rate-limit": "^7.5.0", "google-auth-library": "^9.15.1", "gridfs-stream": "^1.1.1", "helmet": "^8.1.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.10.0", "path": "^0.12.7", "stripe": "^18.2.0", "validator": "^13.15.0"}, "devDependencies": {"nodemon": "^3.1.10"}}